#!/usr/bin/env python3
"""
测试Wan2.2集成的简单脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from prompt_extend import WanPromptExpander, T2V_A14B_ZH_SYS_PROMPT, I2V_A14B_ZH_SYS_PROMPT

def test_wan22_integration():
    """测试Wan2.2集成功能"""
    print("🎨 测试Wan2.2集成功能")
    print("=" * 50)
    
    # 测试T2V模式
    print("\n🎬 测试T2V模式")
    try:
        t2v_expander = WanPromptExpander(
            model_name="wan2.2",
            mode="T2V",
            is_vl=False,
            api_key="sk-test"  # 测试用的假密钥
        )
        
        # 测试系统提示词选择
        system_prompt = t2v_expander.decide_system_prompt(tar_lang="zh")
        print(f"✅ T2V中文系统提示词长度: {len(system_prompt)} 字符")
        print(f"   系统提示词开头: {system_prompt[:100]}...")
        
        system_prompt_en = t2v_expander.decide_system_prompt(tar_lang="en")
        print(f"✅ T2V英文系统提示词长度: {len(system_prompt_en)} 字符")
        print(f"   系统提示词开头: {system_prompt_en[:100]}...")
        
    except Exception as e:
        print(f"❌ T2V模式测试失败: {e}")
    
    # 测试I2V模式
    print("\n🖼️ 测试I2V模式")
    try:
        i2v_expander = WanPromptExpander(
            model_name="wan2.2",
            mode="I2V",
            is_vl=True,
            api_key="sk-test"  # 测试用的假密钥
        )
        
        # 测试系统提示词选择
        system_prompt = i2v_expander.decide_system_prompt(tar_lang="zh")
        print(f"✅ I2V中文系统提示词长度: {len(system_prompt)} 字符")
        print(f"   系统提示词开头: {system_prompt[:100]}...")
        
        system_prompt_en = i2v_expander.decide_system_prompt(tar_lang="en")
        print(f"✅ I2V英文系统提示词长度: {len(system_prompt_en)} 字符")
        print(f"   系统提示词开头: {system_prompt_en[:100]}...")
        
    except Exception as e:
        print(f"❌ I2V模式测试失败: {e}")
    
    # 测试系统提示词内容验证
    print("\n📝 验证系统提示词内容")
    print(f"✅ T2V_A14B_ZH_SYS_PROMPT 已定义，长度: {len(T2V_A14B_ZH_SYS_PROMPT)} 字符")
    print(f"✅ I2V_A14B_ZH_SYS_PROMPT 已定义，长度: {len(I2V_A14B_ZH_SYS_PROMPT)} 字符")
    
    # 验证关键词
    t2v_keywords = ["电影导演", "电影元素", "时间", "光源", "镜头尺寸"]
    i2v_keywords = ["视频描述", "动态内容", "镜头上摇", "运动信息"]
    
    print("\n🔍 验证T2V提示词关键词:")
    for keyword in t2v_keywords:
        if keyword in T2V_A14B_ZH_SYS_PROMPT:
            print(f"   ✅ 包含关键词: {keyword}")
        else:
            print(f"   ❌ 缺少关键词: {keyword}")
    
    print("\n🔍 验证I2V提示词关键词:")
    for keyword in i2v_keywords:
        if keyword in I2V_A14B_ZH_SYS_PROMPT:
            print(f"   ✅ 包含关键词: {keyword}")
        else:
            print(f"   ❌ 缺少关键词: {keyword}")
    
    print("\n" + "=" * 50)
    print("🎉 Wan2.2集成测试完成！")

if __name__ == "__main__":
    test_wan22_integration()