import streamlit as st
import tempfile
import os
import sys
from PIL import Image
import json
import socket
from datetime import datetime

# 添加当前目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from prompt_extend import (
    DashScopePromptExpander,
    SiliconFlowPromptExpander,
    WanPromptExpander
)
from config import get_env_config

def get_server_info():
    """获取服务器信息"""
    try:
        hostname = socket.gethostname()
        return {
            'hostname': hostname,
            'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    except:
        return {
            'hostname': 'unknown',
            'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

def main():
    st.set_page_config(
        page_title="智能Prompt优化器",
        page_icon="🎨",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 添加自定义CSS样式美化页面
    st.markdown("""
    <style>
        /* 主要颜色主题 - 更紧凑的间距 */
        .main > div {
            padding-top: 0.5rem;
        }
        
        /* 减少streamlit默认的容器边距 */
        .block-container {
            padding-top: 1rem;
            padding-bottom: 0.5rem;
            padding-left: 1rem;
            padding-right: 1rem;
        }
        
        /* 美化标题 - 减少padding */
        .title-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 0.8rem;
            border-radius: 8px;
            margin-bottom: 0.8rem;
            text-align: center;
            color: white;
        }
        
        .title-container h1 {
            color: white !important;
            font-size: 1.8rem;
            margin-bottom: 0.2rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            color: #f0f0f0 !important;
            font-size: 0.95rem;
            margin-bottom: 0.3rem;
        }
        
        /* 美化服务器信息 - 减少padding */
        .server-info {
            background: rgba(255,255,255,0.1);
            padding: 0.5rem;
            border-radius: 6px;
            backdrop-filter: blur(10px);
            font-size: 0.8rem;
        }
        
        /* 美化输入区域 - 更紧凑 */
        .input-section {
            background: #f8f9fa;
            padding: 0.8rem;
            border-radius: 8px;
            border-left: 3px solid #667eea;
            margin-bottom: 0.4rem;
        }
        
        /* 美化输出区域 - 更紧凑 */
        .output-section {
            background: #f8f9fa;
            padding: 0.8rem;
            border-radius: 8px;
            border-left: 3px solid #764ba2;
            margin-bottom: 0.4rem;
        }
        
        /* 美化按钮 - 稍微减小 */
        .stButton > button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.3rem 0.6rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
            font-size: 0.85rem;
            height: 2rem;
        }
        
        .stButton > button:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 12px rgba(102, 126, 234, 0.6);
        }
        
        /* 美化成功消息 - 减少margin */
        .success-box {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            padding: 0.6rem;
            border-radius: 6px;
            color: white;
            margin: 0.3rem 0;
        }
        
        /* 美化侧边栏 */
        .css-1d391kg {
            background: #f8f9fa;
        }
        
        /* 侧边栏更紧凑 */
        .css-1lcbmhc {
            padding-top: 1rem;
        }
        
        /* 美化expander - 更紧凑 */
        .streamlit-expanderHeader {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 6px;
            padding: 0.4rem 0.8rem;
            font-size: 0.9rem;
        }
        
        /* 美化页脚 - 减少padding */
        .footer {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            color: white;
            margin-top: 0.8rem;
        }
        
        .footer h3 {
            font-size: 1.3rem;
            margin-bottom: 0.3rem;
        }
        
        .footer p {
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        /* 美化图片上传区域 */
        .uploadedFile {
            border: 2px dashed #667eea;
            border-radius: 6px;
            padding: 0.6rem;
        }
        
        /* 紧凑化文本区域 */
        .stTextArea > div > div > textarea {
            font-size: 0.85rem;
            line-height: 1.3;
        }
        
        /* 紧凑化选择框 */
        .stSelectbox > div > div {
            font-size: 0.85rem;
        }
        
        .stSelectbox label {
            font-size: 0.9rem;
            margin-bottom: 0.2rem;
        }
        
        /* 减少标题间距 */
        .stMarkdown h1, .stMarkdown h2, .stMarkdown h3 {
            margin-top: 0.3rem;
            margin-bottom: 0.3rem;
        }
        
        .stMarkdown h2 {
            font-size: 1.3rem;
        }
        
        .stMarkdown h3 {
            font-size: 1.1rem;
        }
        
        /* 减少分隔线的margin */
        .stMarkdown hr {
            margin: 0.3rem 0;
        }
        
        /* 紧凑化expander内容 */
        .streamlit-expanderContent {
            padding-top: 0.3rem;
            padding-bottom: 0.3rem;
        }
        
        /* 紧凑化columns */
        .stColumns {
            gap: 0.5rem;
        }
        
        /* 减少文件上传器的间距 */
        .stFileUploader label {
            font-size: 0.9rem;
            margin-bottom: 0.3rem;
        }
        
        /* 紧凑化number input */
        .stNumberInput label {
            font-size: 0.9rem;
            margin-bottom: 0.2rem;
        }
        
        /* 紧凑化checkbox */
        .stCheckbox label {
            font-size: 0.9rem;
        }
        
        /* 紧凑化text input */
        .stTextInput label {
            font-size: 0.9rem;
            margin-bottom: 0.2rem;
        }
        
        /* 减少info/success/error框的间距 */
        .stAlert {
            padding: 0.5rem;
            margin: 0.3rem 0;
        }
        
        /* 紧凑化spinner */
        .stSpinner {
            margin: 0.5rem 0;
        }
        
        /* 紧凑化侧边栏标题 */
        .css-1lcbmhc .stMarkdown h2 {
            font-size: 1.1rem;
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        /* 紧凑化侧边栏子标题 */
        .css-1lcbmhc .stMarkdown h3 {
            font-size: 1rem;
            margin-top: 0.3rem;
            margin-bottom: 0.3rem;
        }
        
        /* 进一步减少各种容器的垂直间距 */
        .element-container {
            margin-bottom: 0.3rem;
        }
        
        /* 紧凑化json展示 */
        .stJson {
            font-size: 0.8rem;
        }
        
        /* 减少code块的间距 */
        .stCodeBlock {
            margin: 0.3rem 0;
        }
        
        /* 紧凑化成功/警告/错误提示 */
        .stSuccess, .stWarning, .stError, .stInfo {
            padding: 0.4rem;
            margin: 0.2rem 0;
            font-size: 0.9rem;
        }
        
        /* 减少图片间距 */
        .stImage {
            margin: 0.2rem 0;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .title-container h1 {
                font-size: 1.5rem;
            }
            .subtitle {
                font-size: 0.85rem;
            }
            .input-section, .output-section {
                padding: 0.6rem;
            }
            .block-container {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
            }
        }
    </style>
    """, unsafe_allow_html=True)
    
    # 服务器信息显示
    server_info = get_server_info()
    
    # 美化的页面标题
    st.markdown("""
    <div class="title-container">
        <h1>🎨 智能Prompt优化器</h1>
        <p class="subtitle">支持文本、图片及多模态输入的智能Prompt扩展与优化工具</p>
        <div class="server-info">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>🖥️ 服务器: {}</span>
                <span>🕐 当前时间: {}</span>
            </div>
        </div>
    </div>
    """.format(server_info['hostname'], server_info['time']), unsafe_allow_html=True)
    
    # 添加系统说明
    with st.expander("ℹ️ 关于本工具的工作原理", expanded=False):
        st.markdown("""
        ### 🔧 工作原理说明
        
        本工具使用**专业的内置系统提示词**来优化您的输入：
        
        1. **您的输入**: 在左侧输入框中输入您想要优化的文本
        2. **系统处理**: 使用内置的专业系统提示词（如 `VL_ZH_SYS_PROMPT`）指导AI进行优化
        3. **智能输出**: 输出经过专业优化的高质量Prompt
        
        ### 📝 重要提醒
        - 页面中显示的"示例"是**用户输入示例**，帮助您了解如何使用
        - 真正的**系统提示词**内置在代码中，包含详细的优化规则和专业指令
        - 您可以在处理完成后点击"查看系统提示词"按钮查看实际使用的系统提示词
        """)
    
    # 初始化会话状态
    if 'user_session' not in st.session_state:
        st.session_state.user_session = {
            'id': f"user_{datetime.now().strftime('%H%M%S')}",
            'requests_count': 0,
            'start_time': datetime.now()
        }
    
    # 侧边栏配置
    with st.sidebar:
        st.header("⚙️ 配置选项")
        
        # 用户会话信息
        with st.expander("👤 会话信息", expanded=False):
            st.write(f"**会话ID**: {st.session_state.user_session['id']}")
            st.write(f"**请求次数**: {st.session_state.user_session['requests_count']}")
            st.write(f"**开始时间**: {st.session_state.user_session['start_time'].strftime('%H:%M:%S')}")
        
        # 模型选择
        model_type = st.selectbox(
            "选择模型类型",
            ["SiliconFlow API", "DashScope API", "Wan2.1", "Wan2.2"],
            index=0,  # 默认选择SiliconFlow API
            help="选择不同的模型提供商"
        )
        
        # 输入模式选择
        input_mode = st.selectbox(
            "选择输入模式",
            ["纯文本", "单图片+文本", "多图片+文本"],
            help="根据您的需求选择不同的输入模式"
        )
        
        # 输出语言选择
        output_lang = st.selectbox(
            "输出语言",
            ["中文", "English"],
            help="选择Prompt的输出语言"
        )

        # 双语输出选项
        bilingual_output = st.checkbox(
            "🌍 双语输出",
            value=False,
            help="同时生成中文润色结果和对应的英文提示词"
        )

        # 随机种子
        use_seed = st.checkbox("使用固定随机种子", value=False)
        seed = st.number_input(
            "随机种子",
            min_value=-1,
            max_value=2147483647,
            value=100,
            disabled=not use_seed,
            help="设置固定种子可确保结果可复现"
        )
        
        # API配置
        st.subheader("🔑 API配置")
        if model_type == "DashScope API":
            api_key = st.text_input(
                "DashScope API Key",
                type="password",
                value=os.environ.get("DASH_API_KEY", ""),
                help="请输入您的DashScope API密钥"
            )
            model_name = st.selectbox(
                "选择模型",
                ["qwen-plus", "qwen-vl-max"] if input_mode != "纯文本" else ["qwen-plus"],
                help="根据输入模式选择相应的模型"
            )
        elif model_type == "Wan2.1":
            # 获取环境配置，优先使用配置文件中的API密钥
            env_config = get_env_config()
            api_key = st.text_input(
                "SiliconFlow API Key",
                type="password",
                value=env_config.get("siliconflow_api_key", ""),
                help="请输入您的SiliconFlow API密钥（Wan2.1使用SiliconFlow API）"
            )
            
            # Wan2.1只有一种模式，设置model_name
            model_name = "wan2.1"
            
            st.info("💡 Wan2.1模式：生成正向和负向Prompt对，适合视频生成")
            
        elif model_type == "Wan2.2":
            # 获取环境配置，优先使用配置文件中的API密钥
            env_config = get_env_config()
            api_key = st.text_input(
                "SiliconFlow API Key",
                type="password",
                value=env_config.get("siliconflow_api_key", ""),
                help="请输入您的SiliconFlow API密钥（Wan2.2使用SiliconFlow API）"
            )
            
            # Wan2.2模式选择
            wan_mode = st.selectbox(
                "选择Wan2.2模式",
                ["T2V (Text to Video)", "I2V (Image to Video)"],
                index=0 if input_mode == "纯文本" else 1,
                help="T2V模式：纯文本输入；I2V模式：图像+文本输入"
            )
            
            # 根据模式设置model_name
            model_name = "wan2.2"
            
            # 如果选择了I2V模式但输入模式是纯文本，提示用户
            if wan_mode.startswith("I2V") and input_mode == "纯文本":
                st.warning("⚠️ I2V模式需要图像输入，请在输入模式中选择包含图片的选项")
            
            # 如果选择了T2V模式但输入模式包含图片，提示用户  
            if wan_mode.startswith("T2V") and input_mode != "纯文本":
                st.info("💡 T2V模式只处理文本，上传的图片将被忽略")
                
        else:  # SiliconFlow API
            # 获取环境配置，优先使用配置文件中的API密钥
            env_config = get_env_config()
            api_key = st.text_input(
                "SiliconFlow API Key",
                type="password",
                value=env_config.get("siliconflow_api_key", ""),
                help="请输入您的SiliconFlow API密钥（默认使用配置文件中的密钥）"
            )
            if input_mode == "纯文本":
                model_name = st.selectbox(
                    "选择模型",
                    ["deepseek-ai/DeepSeek-V3", "Qwen/Qwen2.5-72B-Instruct"],
                    help="选择文本生成模型"
                )
            else:
                model_name = st.selectbox(
                    "选择模型",
                    ["Qwen/Qwen2.5-VL-72B-Instruct", "Qwen/Qwen2.5-VL-32B-Instruct"],
                    help="选择视觉语言模型"
                )

    # 主内容区域
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.markdown('<div class="input-section">', unsafe_allow_html=True)
        st.header("📝 输入区域")
        
        # 文本输入
        prompt_text = st.text_area(
            "输入Prompt文本",
            height=80,
            placeholder="请输入您想要优化的Prompt文本...",
            help="输入您的原始Prompt，系统将自动进行优化和扩展"
        )
        
        # 图片上传区域
        uploaded_images = []
        if input_mode in ["单图片+文本", "多图片+文本"]:
            st.subheader("🖼️ 图片上传")
            
            if input_mode == "单图片+文本":
                uploaded_file = st.file_uploader(
                    "上传图片",
                    type=['png', 'jpg', 'jpeg'],
                    help="上传一张图片以辅助Prompt优化"
                )
                if uploaded_file is not None:
                    uploaded_images = [uploaded_file]
                    st.image(uploaded_file, caption="已上传的图片", width=300)
            
            else:  # 多图片模式
                uploaded_files = st.file_uploader(
                    "上传多张图片",
                    type=['png', 'jpg', 'jpeg'],
                    accept_multiple_files=True,
                    help="上传多张图片（建议上传视频的第一帧和最后一帧）"
                )
                if uploaded_files:
                    uploaded_images = uploaded_files
                    st.write(f"已上传 {len(uploaded_files)} 张图片:")
                    cols = st.columns(min(len(uploaded_files), 4))
                    for i, file in enumerate(uploaded_files):
                        with cols[i % 4]:
                            st.image(file, caption=f"图片 {i+1}", width=200)
        
        # 执行按钮
        execute_button = st.button(
            "🚀 开始优化Prompt",
            type="primary",
            disabled=not prompt_text.strip()
        )
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown('<div class="output-section">', unsafe_allow_html=True)
        st.header("📊 输出结果")
        
        if execute_button and prompt_text.strip():
            # 更新请求计数
            st.session_state.user_session['requests_count'] += 1
            
            try:
                with st.spinner("正在处理中，请稍候..."):
                    # 初始化模型
                    if model_type == "DashScope API":
                        expander = DashScopePromptExpander(
                            api_key=api_key if api_key else None,
                            model_name=model_name,
                            is_vl=(input_mode != "纯文本")
                        )
                    elif model_type == "Wan2.1":
                        # Wan2.1使用SiliconFlow API，但使用特殊的系统提示词
                        expander = SiliconFlowPromptExpander(
                            api_key=api_key if api_key else None,
                            model_name="deepseek-ai/DeepSeek-V3",  # Wan2.1使用的默认模型
                            is_vl=False  # Wan2.1主要是文本模式
                        )
                        # 手动设置Wan2.1的系统提示词
                        from prompt_extend import WAN21_SYS_PROMPT
                        expander._wan21_mode = True
                        expander._wan21_system_prompt = WAN21_SYS_PROMPT
                    elif model_type == "Wan2.2":
                        # 确定Wan2.2的模式
                        wan_mode_type = "T2V" if wan_mode.startswith("T2V") else "I2V"
                        expander = WanPromptExpander(
                            api_key=api_key if api_key else None,
                            model_name=model_name,
                            mode=wan_mode_type,
                            is_vl=(wan_mode_type == "I2V")
                        )
                    elif model_type == "SiliconFlow API":
                        expander = SiliconFlowPromptExpander(
                            api_key=api_key if api_key else None,
                            model_name=model_name,
                            is_vl=(input_mode != "纯文本")
                        )
                    
                    # 处理图片
                    images = None
                    if uploaded_images:
                        images = []
                        for uploaded_file in uploaded_images:
                            # 保存临时文件
                            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{uploaded_file.name.split('.')[-1]}") as tmp_file:
                                tmp_file.write(uploaded_file.getvalue())
                                tmp_path = tmp_file.name
                            
                            # 加载图片
                            img = Image.open(tmp_path)
                            images.append(img)
                            
                            # 清理临时文件
                            os.unlink(tmp_path)
                    
                    # 执行优化
                    target_lang = "zh" if output_lang == "中文" else "en"
                    final_seed = seed if use_seed else -1
                    
                    # 特殊处理Wan2.1模式
                    if model_type == "Wan2.1":
                        # Wan2.1使用特殊的系统提示词
                        result = expander(
                            prompt=prompt_text,
                            system_prompt=expander._wan21_system_prompt,
                            tar_lang=target_lang,
                            image=images,
                            seed=final_seed,
                            bilingual=bilingual_output
                        )
                    else:
                        # 其他模式正常调用
                        result = expander(
                            prompt=prompt_text,
                            tar_lang=target_lang,
                            image=images,
                            seed=final_seed,
                            bilingual=bilingual_output
                        )
                    
                    # 显示结果
                    if result.status:
                        st.success("✅ Prompt优化完成！")

                        if result.is_bilingual:
                            # 双语输出显示
                            st.subheader("🌍 双语优化结果")

                            # 中文结果
                            st.markdown("**🇨🇳 中文润色结果:**")
                            st.text_area(
                                "",
                                value=result.prompt_zh,
                                height=120,
                                help="中文润色后的Prompt",
                                key="chinese_result"
                            )

                            # 英文结果
                            st.markdown("**🇺🇸 英文提示词:**")
                            st.text_area(
                                "",
                                value=result.prompt_en,
                                height=120,
                                help="对应的英文Prompt",
                                key="english_result"
                            )

                            # 复制按钮
                            col1, col2 = st.columns(2)
                            with col1:
                                if st.button("📋 复制中文结果"):
                                    st.write("请手动复制上面的中文文本")
                            with col2:
                                if st.button("📋 复制英文结果"):
                                    st.write("请手动复制上面的英文文本")
                        else:
                            # 单语输出显示
                            st.subheader("🎯 优化后的Prompt")
                            st.text_area(
                                "",
                                value=result.prompt,
                                height=120,
                                help="这是优化后的Prompt，可以直接复制使用"
                            )

                            # 复制按钮
                            if st.button("📋 复制到剪贴板"):
                                st.write("请手动复制上面的文本")
                        
                        # 详细信息
                        with st.expander("📋 详细信息"):
                            st.write("**使用的随机种子:**", result.seed)
                            st.write("**模型类型:**", model_type)
                            st.write("**输入模式:**", input_mode)
                            st.write("**输出语言:**", output_lang)
                            st.write("**双语输出:**", "是" if result.is_bilingual else "否")
                            
                            # 响应信息显示
                            st.markdown("---")
                            st.subheader("🔍 响应信息查看")
                            
                            # 创建按钮组
                            col1, col2, col3, col4 = st.columns(4)

                            with col1:
                                show_system_prompt = st.button("📜 系统提示词")

                            with col2:
                                show_raw_response = st.button("📄 原始响应")

                            with col3:
                                show_call_history = st.button("🔍 调用历史")

                            with col4:
                                show_request_info = st.button("⚙️ 请求信息")
                            
                            # 显示对应的信息
                            if show_system_prompt:
                                st.markdown("**📜 使用的系统提示词:**")
                                with st.container():
                                    st.text_area(
                                        "",
                                        value=result.system_prompt,
                                        height=120,
                                        help="这是AI用于优化您输入的专业系统提示词"
                                    )
                            
                            elif show_raw_response:
                                st.markdown("**📄 API原始响应:**")
                                
                                # 首先尝试从call_history获取最新的成功响应
                                latest_response = None
                                if hasattr(result, 'call_history') and result.call_history:
                                    for call in reversed(result.call_history):
                                        if call.get('status') == 'success' and 'response' in call:
                                            latest_response = call['response']
                                            break
                                
                                if latest_response:
                                    st.markdown("**🎯 最新成功响应 (来自调用历史):**")
                                    if isinstance(latest_response, dict):
                                        st.json(latest_response)
                                    else:
                                        st.code(str(latest_response), language='json')
                                    
                                    # 同时显示原始message内容作为对比
                                    with st.expander("查看原始message字段内容", expanded=False):
                                        st.text_area(
                                            "",
                                            value=result.message,
                                            height=100,
                                            help="这是result.message字段的原始内容"
                                        )
                                else:
                                    # 原有的处理逻辑
                                    try:
                                        # 首先尝试解析为JSON
                                        response_data = json.loads(result.message)
                                        st.json(response_data)
                                    except json.JSONDecodeError:
                                        # 如果不是有效JSON，检查是否包含多个JSON响应
                                        try:
                                            # 尝试分离多个JSON对象
                                            message_content = result.message.strip()
                                            if message_content.startswith('{') and message_content.endswith('}'):
                                                # 尝试找到最后一个完整的JSON对象（通常是最终成功的响应）
                                                brace_count = 0
                                                last_complete_json_end = -1
                                                for i, char in enumerate(message_content):
                                                    if char == '{':
                                                        brace_count += 1
                                                    elif char == '}':
                                                        brace_count -= 1
                                                        if brace_count == 0:
                                                            last_complete_json_end = i
                                                
                                                if last_complete_json_end > 0:
                                                    # 提取最后一个完整的JSON
                                                    last_json_start = message_content.rfind('{', 0, last_complete_json_end + 1)
                                                    if last_json_start >= 0:
                                                        last_json = message_content[last_json_start:last_complete_json_end + 1]
                                                        try:
                                                            parsed_json = json.loads(last_json)
                                                            st.markdown("**🎯 最终API响应 (JSON格式):**")
                                                            st.json(parsed_json)
                                                            
                                                            # 如果还有其他内容，也显示原始内容
                                                            if len(message_content) > len(last_json):
                                                                st.markdown("**📋 完整原始响应内容:**")
                                                                st.text_area(
                                                                    "",
                                                                    value=result.message,
                                                                    height=100,
                                                                    help="包含所有API调用的完整响应信息"
                                                                )
                                                            return
                                                        except json.JSONDecodeError:
                                                            # 如果以上都失败，显示原始文本
                                                            st.markdown("**📋 原始响应内容 (文本格式):**")
                                                            st.text_area(
                                                                "",
                                                                value=result.message,
                                                                height=100,
                                                                help="原始响应信息（非JSON格式或包含多个响应）"
                                                            )
                                            
                                            # 如果以上都失败，显示原始文本
                                            st.markdown("**📋 原始响应内容 (文本格式):**")
                                            st.text_area(
                                                "",
                                                value=result.message,
                                                height=100,
                                                help="原始响应信息（非JSON格式或包含多个响应）"
                                            )
                                        except Exception as e:
                                            # 处理其他异常
                                            st.error(f"处理响应数据时出错: {str(e)}")
                                            st.markdown("**📋 原始响应内容:**")
                                            st.text_area(
                                                "",
                                                value=result.message,
                                                height=100,
                                                help="原始响应信息"
                                            )
                            
                            elif show_call_history:
                                st.markdown("**🔍 API调用历史:**")
                                if hasattr(result, 'call_history') and result.call_history:
                                    st.markdown(f"**总共尝试次数**: {len(result.call_history)}")
                                    
                                    for i, call in enumerate(result.call_history, 1):
                                        with st.expander(f"第 {call.get('attempt', i)} 次调用 - {call.get('status', '未知状态').upper()}", expanded=(i == len(result.call_history))):
                                            st.write(f"**时间**: {call.get('timestamp', 'N/A')}")
                                            st.write(f"**状态**: {call.get('status', 'N/A')}")
                                            
                                            if call.get('status') == 'success':
                                                if 'response' in call:
                                                    st.markdown("**✅ 成功响应:**")
                                                    if isinstance(call['response'], dict):
                                                        st.json(call['response'])
                                                    else:
                                                        st.code(str(call['response']), language='json')
                                                
                                                if 'status_code' in call:
                                                    st.write(f"**HTTP状态码**: {call['status_code']}")
                                            
                                            elif call.get('status') == 'failed':
                                                st.markdown("**❌ 失败信息:**")
                                                st.error(call.get('error', '未知错误'))
                                                if 'status_code' in call and call['status_code']:
                                                    st.write(f"**HTTP状态码**: {call['status_code']}")
                                                if 'response' in call:
                                                    st.write("**错误响应内容:**")
                                                    st.code(str(call['response']), language='text')
                                            
                                            # 显示请求参数（如果有）
                                            if 'request_payload' in call:
                                                with st.expander("查看请求参数", expanded=False):
                                                    # 移除敏感信息
                                                    safe_payload = call['request_payload'].copy()
                                                    if 'messages' in safe_payload:
                                                        # 简化messages显示，避免过长
                                                        for msg in safe_payload['messages']:
                                                            if isinstance(msg.get('content'), list):
                                                                msg['content'] = f"[包含 {len(msg['content'])} 个元素的复合内容]"
                                                    st.json(safe_payload)
                                            
                                            # 其他信息
                                            if call.get('model_type'):
                                                st.write(f"**模型类型**: {call['model_type']}")
                                            if call.get('model_name'):
                                                st.write(f"**模型名称**: {call['model_name']}")
                                            if call.get('image_count'):
                                                st.write(f"**图片数量**: {call['image_count']}")
                                            if call.get('generated_tokens'):
                                                st.write(f"**生成token数**: {call['generated_tokens']}")
                                else:
                                    st.info("⚠️ 此次调用没有详细的历史记录")
                                    st.markdown("这可能是因为：")
                                    st.markdown("- 使用的是旧版本的代码")
                                    st.markdown("- 调用过程中出现了意外错误")
                                    st.markdown("- 模型类型不支持详细历史记录")
                            
                            elif show_request_info:
                                st.markdown("**⚙️ 请求详细信息:**")
                                request_info = {
                                    "模型类型": model_type,
                                    "模型名称": model_name if 'model_name' in locals() else "N/A",
                                    "输入模式": input_mode,
                                    "输出语言": output_lang,
                                    "随机种子": result.seed,
                                    "图片数量": len(uploaded_images) if uploaded_images else 0,
                                    "请求状态": "成功" if result.status else "失败",
                                    "处理时间": "实时",
                                    "会话ID": st.session_state.user_session['id']
                                }
                                
                                for key, value in request_info.items():
                                    st.write(f"**{key}:** {value}")
                            
                            else:
                                st.info("👆 点击上方按钮查看不同类型的响应信息")
                    
                    else:
                        st.error("❌ 处理失败")
                        st.error(f"错误信息: {result.message}")
                        
            except Exception as e:
                st.error(f"❌ 发生错误: {str(e)}")
                st.exception(e)
        
        elif not prompt_text.strip():
            st.info("💡 请在左侧输入Prompt文本开始使用")
        
        else:
            # 显示示例
            st.subheader("💡 使用示例")
            
            # 添加说明文本
            st.info("ℹ️ **重要说明**: 以下是**用户输入示例**，帮助您了解如何使用不同模式。这些不是系统内部的提示词，而是您可以在左侧输入框中尝试的示例内容。")
            
            st.markdown("---")
            
            examples = {
                "纯文本": {
                    "prompt": "夏日海滩度假风格，一只戴着墨镜的白色猫咪坐在冲浪板上",
                    "description": "简单的文本描述，系统会自动扩展细节"
                },
                "单图片+文本": {
                    "prompt": "日系小清新胶片写真，根据图片中的人物和场景，展现自然温馨的生活画面",
                    "description": "结合上传的图片内容进行专业级Prompt优化"
                },
                "多图片+文本": {
                    "prompt": "无人机拍摄，镜头快速推进然后拉远至全景俯瞰",
                    "description": "结合多张图片（如视频首尾帧）进行动态描述"
                }
            }
            
            # 如果是Wan模式，显示特定的示例
            if model_type == "Wan2.1":
                wan21_example = {
                    "prompt": "一个宁静的海港，停满了游艇，水面清澈透蓝",
                    "description": "Wan2.1模式：生成正向和负向Prompt对，专为视频生成优化"
                }
                example = wan21_example
                st.success(f"**Wan2.1模式示例:**")
            elif model_type == "Wan2.2":
                wan_mode_type = "T2V" if wan_mode.startswith("T2V") else "I2V"
                wan_examples = {
                    "T2V": {
                        "prompt": "一只可爱的小猫在阳光明媚的花园里玩耍",
                        "description": "T2V模式：纯文本输入，添加电影级美学元素，适合视频生成"
                    },
                    "I2V": {
                        "prompt": "根据图片内容，描述人物的动作和表情变化",
                        "description": "I2V模式：基于图像内容优化动态描述，结合视觉信息"
                    }
                }
                example = wan_examples.get(wan_mode_type, wan_examples["T2V"])
                st.success(f"**Wan2.2 {wan_mode_type}模式示例:**")
            else:
                example = examples.get(input_mode, examples["纯文本"])
                st.success(f"**{input_mode}模式示例:**")
            st.write(f"**📝 用户输入示例:** {example['prompt']}")
            st.write(f"**📋 使用说明:** {example['description']}")
            
            # 添加系统提示词说明
            with st.expander("🔍 关于系统提示词", expanded=False):
                if model_type == "Wan2.1":
                    st.markdown("""
                    **什么是Wan2.1系统提示词？**
                    
                    Wan2.1是专为视频生成优化的Prompt系统，包含专业指令：
                    
                    - **WAN21_SYS_PROMPT**: 专业的视频生成优化指令
                    
                    Wan2.1的特点：
                    - 生成正向和负向Prompt对
                    - 专为Wan2.1视频生成模型设计
                    - 包含详细的场景分析和构图指导
                    - 支持电影级视觉效果描述
                    
                    **上面显示的只是用户输入示例**，帮助您了解如何使用Wan2.1工具。
                    """)
                elif model_type == "Wan2.2":
                    st.markdown("""
                    **什么是Wan2.2系统提示词？**
                    
                    Wan2.2是专为视频生成优化的Prompt系统，包含以下专业指令：
                    
                    - **T2V_A14B_ZH_SYS_PROMPT**: T2V模式中文电影级优化指令
                    - **T2V_A14B_EN_SYS_PROMPT**: T2V模式英文电影级优化指令
                    - **I2V_A14B_ZH_SYS_PROMPT**: I2V模式中文视频描述优化指令
                    - **I2V_A14B_EN_SYS_PROMPT**: I2V模式英文视频描述优化指令
                    
                    这些系统提示词专注于：
                    - 添加电影美学元素（光线、构图、镜头运动等）
                    - 强调动态内容和运镜描述
                    - 优化视频生成的专业表达
                    
                    **上面显示的只是用户输入示例**，帮助您了解如何使用Wan2.2工具。
                    """)
                else:
                    st.markdown("""
                    **什么是系统提示词？**
                    
                    系统提示词是内置在 `prompt_extend.py` 中的专业指令，用于指导AI如何优化您的输入。例如：
                    
                    - **LM_ZH_SYS_PROMPT**: 用于中文文本优化的专业指令
                    - **VL_ZH_SYS_PROMPT**: 用于中文图文结合优化的专业指令  
                    - **VL_ZH_SYS_PROMPT_FOR_MULTI_IMAGES**: 用于多图片优化的专业指令
                    
                    这些系统提示词包含详细的任务要求、格式规范和专业示例，确保输出高质量的优化结果。
                    
                    **上面显示的只是用户输入示例**，帮助您了解如何使用本工具。
                    """)
        
        st.markdown('</div>', unsafe_allow_html=True)

    # 美化的页脚
    st.markdown("""
    <div class="footer">
        <h3>🎨 智能Prompt优化器</h3>
        <p>让您的创意更精彩 | 支持多种API服务</p>
        <div style="margin-top: 0.5rem; padding-top: 0.5rem; border-top: 1px solid rgba(255,255,255,0.2);">
            <small>© 2024 智能Prompt优化器</small>
        </div>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main() 