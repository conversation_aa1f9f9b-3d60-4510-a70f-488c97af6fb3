#!/usr/bin/env python3
"""
测试Wan2.1和Wan2.2集成的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from prompt_extend import WanPromptExpander, WAN21_SYS_PROMPT, T2V_A14B_ZH_SYS_PROMPT, I2V_A14B_ZH_SYS_PROMPT

def test_wan_integration():
    """测试Wan2.1和Wan2.2集成功能"""
    print("🎨 测试Wan2.1和Wan2.2集成功能")
    print("=" * 60)
    
    # 测试Wan2.1系统提示词
    print("\n🎬 测试Wan2.1")
    try:
        print(f"✅ WAN21_SYS_PROMPT 已定义，长度: {len(WAN21_SYS_PROMPT)} 字符")
        print(f"   提示词开头: {WAN21_SYS_PROMPT[:120]}...")
        
        # 验证Wan2.1关键词
        wan21_keywords = ["Wan2.1 video generation model", "Positive Prompt", "Negative Prompt", "scenario"]
        print("\n🔍 验证Wan2.1提示词关键词:")
        for keyword in wan21_keywords:
            if keyword in WAN21_SYS_PROMPT:
                print(f"   ✅ 包含关键词: {keyword}")
            else:
                print(f"   ⚠️  关键词变体: {keyword}")
                
    except NameError as e:
        print(f"❌ Wan2.1提示词导入失败: {e}")
    except Exception as e:
        print(f"❌ Wan2.1测试失败: {e}")
    
    # 测试Wan2.2 T2V模式
    print("\n🎥 测试Wan2.2 T2V模式")
    try:
        t2v_expander = WanPromptExpander(
            model_name="wan2.2",
            mode="T2V",
            is_vl=False,
            api_key="sk-test"  # 测试用的假密钥
        )
        
        system_prompt = t2v_expander.decide_system_prompt(tar_lang="zh")
        print(f"✅ T2V中文系统提示词长度: {len(system_prompt)} 字符")
        print(f"   系统提示词开头: {system_prompt[:100]}...")
        
    except Exception as e:
        print(f"❌ Wan2.2 T2V模式测试失败: {e}")
    
    # 测试Wan2.2 I2V模式
    print("\n🖼️ 测试Wan2.2 I2V模式")
    try:
        i2v_expander = WanPromptExpander(
            model_name="wan2.2",
            mode="I2V",
            is_vl=True,
            api_key="sk-test"  # 测试用的假密钥
        )
        
        system_prompt = i2v_expander.decide_system_prompt(tar_lang="zh")
        print(f"✅ I2V中文系统提示词长度: {len(system_prompt)} 字符")
        print(f"   系统提示词开头: {system_prompt[:100]}...")
        
    except Exception as e:
        print(f"❌ Wan2.2 I2V模式测试失败: {e}")
    
    # 测试导入
    print("\n📦 测试应用导入")
    try:
        import app
        print("✅ app.py 导入成功")
        
        # 检查新的导入
        from prompt_extend import WAN21_SYS_PROMPT
        print("✅ WAN21_SYS_PROMPT 导入成功")
        
    except Exception as e:
        print(f"❌ 应用导入失败: {e}")
    
    # 版本对比
    print("\n📊 版本功能对比")
    print("┌─────────────┬────────────────┬────────────────┐")
    print("│   版本      │   主要功能     │   系统提示词   │")
    print("├─────────────┼────────────────┼────────────────┤")
    print("│   Wan2.1    │ 正负向Prompt对 │ WAN21_SYS_PROMPT │")
    print("│   Wan2.2    │ T2V/I2V模式   │ T2V/I2V_A14B_* │")
    print("└─────────────┴────────────────┴────────────────┘")
    
    print("\n" + "=" * 60)
    print("🎉 Wan2.1和Wan2.2集成测试完成！")
    print("💡 现在用户可以在以下选项中选择：")
    print("   - SiliconFlow API")
    print("   - DashScope API") 
    print("   - Wan2.1 (正负向Prompt对)")
    print("   - Wan2.2 (T2V/I2V模式)")

if __name__ == "__main__":
    test_wan_integration()